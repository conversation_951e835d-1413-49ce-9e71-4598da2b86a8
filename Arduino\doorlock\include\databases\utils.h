#ifndef HW_S_UTILS_F_H
#define HW_S_UTILS_F_H

#include "../common.h"
#include "../super_password.h"
#include "../utils/aes_ctr.h"
#include "../utils/utils.h"
#include "../utils/json.h"
#include "../utils/wifi_s.h"
#include "../utils/mqtt_s.h"
#include "initialize.h"
#include "configuration_database.h"
#include "counter.h"

struct MotorConfig
{
  String direction_1;
  String direction_2;
  String time_1;
  String time_2;
  String off_time;
};

// 获取电机配置参数
MotorConfig get_motor_config();

// 设置电机配置参数
bool set_motor_config(MotorConfig config);

// 获取WiFi配置参数
WiFiConfig get_wifi_config();

// 设置WiFi配置参数
bool set_wifi_config(WiFiConfig config);

// 获取MQTT配置参数
MQTTConfig get_mqtt_config();

// 设置MQTT配置参数
bool set_mqtt_config(MQTTConfig config);

// 获取设备名称（用户设置），custom_device_name 的长度建议至少为 50 位
bool get_custom_device_name(char *custom_device_name);

// 获取设备使用模式
int get_device_mode();

// 设置设备使用模式
bool set_device_mode(int device_mode);

// 获取超级管理员密码
bool get_super_password(String &password);

// 获取管理员密码
bool get_admin_password(String &password);

// 修改管理员密码
bool modify_admin_password(String &password);

#endif
